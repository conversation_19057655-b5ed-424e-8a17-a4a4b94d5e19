#!/usr/bin/env python3
"""
调试文本提取功能
"""

import os
import asyncio
import sys
from douyin_mcp_server.server import DouyinProcessor

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-d4d314c9749c474d95efbb2e85b0769b'

def test_api_key():
    """测试API密钥"""
    print("🔑 测试API密钥...")
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if api_key:
        print(f"✅ API密钥已设置: {api_key[:10]}...")
        return True
    else:
        print("❌ API密钥未设置")
        return False

def test_video_parsing():
    """测试视频解析"""
    print("\n📱 测试视频解析...")
    share_link = "https://v.douyin.com/VNWSjQzeseU/"
    
    try:
        processor = DouyinProcessor("")  # 解析不需要API密钥
        video_info = processor.parse_share_url(share_link)
        print(f"✅ 视频ID: {video_info['video_id']}")
        print(f"✅ 标题: {video_info['title'][:50]}...")
        print(f"✅ URL: {video_info['url'][:50]}...")
        return video_info
    except Exception as e:
        print(f"❌ 视频解析失败: {str(e)}")
        return None

def test_dashscope_import():
    """测试dashscope导入"""
    print("\n📦 测试dashscope模块...")
    try:
        import dashscope
        print(f"✅ dashscope版本: {dashscope.__version__ if hasattr(dashscope, '__version__') else '未知'}")
        return True
    except ImportError as e:
        print(f"❌ dashscope导入失败: {str(e)}")
        return False

def test_text_extraction_simple():
    """简单测试文本提取"""
    print("\n🎧 测试文本提取...")
    
    if not test_api_key():
        return False
    
    if not test_dashscope_import():
        return False
    
    video_info = test_video_parsing()
    if not video_info:
        return False
    
    try:
        processor = DouyinProcessor(os.getenv('DASHSCOPE_API_KEY'))
        print("⏳ 开始文本提取...")
        
        # 设置较短的超时时间进行测试
        import dashscope
        dashscope.api_key = os.getenv('DASHSCOPE_API_KEY')
        
        print(f"🔗 视频URL: {video_info['url']}")
        
        # 尝试调用API
        task_response = dashscope.audio.asr.Transcription.async_call(
            model="paraformer-v2",
            file_urls=[video_info['url']],
            language_hints=['zh', 'en']
        )
        
        print(f"✅ 任务提交成功，任务ID: {task_response.output.task_id}")
        print("⏳ 等待转录完成...")
        
        # 等待结果
        transcription_response = dashscope.audio.asr.Transcription.wait(
            task=task_response.output.task_id
        )
        
        if transcription_response.status_code == 200:
            print("✅ 转录完成！")
            # 获取结果
            for transcription in transcription_response.output['results']:
                url = transcription['transcription_url']
                print(f"📄 结果URL: {url}")
                
                import json
                from urllib import request
                result = json.loads(request.urlopen(url).read().decode('utf8'))
                
                if 'transcripts' in result and len(result['transcripts']) > 0:
                    text_content = result['transcripts'][0]['text']
                    print(f"📝 提取的文本: {text_content}")
                    
                    # 保存结果
                    with open('debug_result.txt', 'w', encoding='utf-8') as f:
                        f.write(text_content)
                    print("💾 结果已保存到 debug_result.txt")
                    return True
                else:
                    print("⚠️ 未识别到文本内容")
                    return False
        else:
            print(f"❌ 转录失败: {transcription_response.output.message}")
            return False
            
    except Exception as e:
        print(f"❌ 文本提取失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 抖音文本提取调试工具")
    print("=" * 50)
    
    success = test_text_extraction_simple()
    
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n💥 测试失败，请检查配置。")
