#!/usr/bin/env python3
"""
基础测试脚本
"""

import os
import json
from datetime import datetime

def main():
    # 直接写入文件进行测试
    with open('test_output.txt', 'w', encoding='utf-8') as f:
        f.write(f"测试开始 - {datetime.now()}\n")
        
        # 检查JSON文件
        json_file = "json/search_contents_2025-09-11.json"
        f.write(f"检查文件: {json_file}\n")
        
        if os.path.exists(json_file):
            f.write("文件存在\n")
            
            try:
                with open(json_file, 'r', encoding='utf-8') as jf:
                    data = json.load(jf)
                f.write(f"JSON加载成功，包含 {len(data)} 个项目\n")
                
                # 显示前几个项目
                for i, item in enumerate(data[:3], 1):
                    f.write(f"项目 {i}:\n")
                    f.write(f"  标题: {item.get('title', 'N/A')}\n")
                    f.write(f"  作者: {item.get('nickname', 'N/A')}\n")
                    f.write(f"  链接: {item.get('aweme_url', 'N/A')}\n")
                    f.write(f"  下载链接: {item.get('video_download_url', 'N/A')}\n")
                    f.write("\n")
                
            except Exception as e:
                f.write(f"JSON加载失败: {str(e)}\n")
        else:
            f.write("文件不存在\n")
        
        # 检查API密钥
        api_key = os.getenv('DASHSCOPE_API_KEY')
        if api_key:
            f.write(f"API密钥已设置: {api_key[:10]}...\n")
        else:
            f.write("API密钥未设置\n")
        
        # 检查依赖
        try:
            import dashscope
            f.write("dashscope 模块导入成功\n")
        except ImportError as e:
            f.write(f"dashscope 模块导入失败: {str(e)}\n")
        
        try:
            import requests
            f.write("requests 模块导入成功\n")
        except ImportError as e:
            f.write(f"requests 模块导入失败: {str(e)}\n")
        
        f.write(f"测试完成 - {datetime.now()}\n")

if __name__ == "__main__":
    main()
    print("测试完成，请查看 test_output.txt 文件")
