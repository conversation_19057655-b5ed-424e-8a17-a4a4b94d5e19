#!/usr/bin/env python3
"""
工作版本的批量处理器
"""

import os
import json
import time
import asyncio
import requests
from datetime import datetime
from http import HTTPStatus
import dashscope

def setup_api():
    """设置API密钥"""
    api_key = 'sk-d4d314c9749c474d95efbb2e85b0769b'
    os.environ['DASHSCOPE_API_KEY'] = api_key
    dashscope.api_key = api_key
    return api_key

def log_to_file(message, filename='batch_log.txt'):
    """记录日志到文件"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_msg = f"[{timestamp}] {message}"
    
    with open(filename, 'a', encoding='utf-8') as f:
        f.write(log_msg + '\n')
    
    print(log_msg)  # 同时输出到控制台

async def extract_text_from_video(video_url, model="paraformer-v2"):
    """从视频URL提取文字"""
    try:
        log_to_file(f"开始语音识别: {video_url[:80]}...")
        
        # 使用阿里云百炼API进行语音识别
        recognition = dashscope.audio.asr.Recognition()
        result = recognition.call(
            model=model,
            file_urls=[video_url],
            language_hints=['zh', 'en']
        )
        
        if result.status_code == HTTPStatus.OK:
            if hasattr(result.output, 'results') and result.output.results:
                # 提取所有文本片段
                text_segments = []
                for item in result.output.results:
                    if hasattr(item, 'transcripts') and item.transcripts:
                        for transcript in item.transcripts:
                            if hasattr(transcript, 'text'):
                                text_segments.append(transcript.text)
                
                full_text = ' '.join(text_segments).strip()
                if full_text:
                    log_to_file(f"文字提取成功，长度: {len(full_text)} 字符")
                    return full_text
                else:
                    return "未检测到语音内容"
            else:
                return "语音识别结果为空"
        else:
            error_msg = f"API调用失败: {result.message}"
            log_to_file(error_msg)
            return f"提取失败: {error_msg}"
            
    except Exception as e:
        error_msg = f"文字提取出错: {str(e)}"
        log_to_file(error_msg)
        return error_msg

async def process_videos_from_json():
    """处理JSON文件中的视频"""
    # 清空日志文件
    with open('batch_log.txt', 'w', encoding='utf-8') as f:
        f.write(f"批量处理开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    log_to_file("启动批量视频文字提取工具")
    
    # 设置API密钥
    api_key = setup_api()
    log_to_file(f"API密钥已设置: {api_key[:10]}...")
    
    # 读取JSON文件
    json_file = "json/search_contents_2025-09-11.json"
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        log_to_file(f"成功加载 {json_file}，包含 {len(data)} 个视频")
    except Exception as e:
        log_to_file(f"加载文件失败: {str(e)}")
        return
    
    # 提取视频信息
    videos = []
    for item in data:
        if item.get('aweme_url'):
            videos.append({
                'aweme_id': item.get('aweme_id', ''),
                'title': item.get('title', '无标题'),
                'nickname': item.get('nickname', '未知用户'),
                'aweme_url': item.get('aweme_url', ''),
                'video_download_url': item.get('video_download_url', ''),
                'liked_count': item.get('liked_count', '0'),
                'comment_count': item.get('comment_count', '0'),
                'share_count': item.get('share_count', '0')
            })
    
    log_to_file(f"找到 {len(videos)} 个有效视频")
    
    # 处理结果
    results = []
    success_count = 0
    failed_count = 0
    
    # 处理所有视频
    log_to_file(f"开始处理 {len(videos)} 个视频")
    
    for i, video in enumerate(videos, 1):
        log_to_file(f"处理视频 {i}/{len(videos)}: {video['title'][:50]}...")
        
        result = {
            'index': i,
            'aweme_id': video['aweme_id'],
            'title': video['title'],
            'nickname': video['nickname'],
            'aweme_url': video['aweme_url'],
            'video_download_url': video['video_download_url'],
            'liked_count': video['liked_count'],
            'comment_count': video['comment_count'],
            'share_count': video['share_count'],
            'extracted_text': '',
            'status': 'failed',
            'error': '',
            'process_time': datetime.now().isoformat()
        }
        
        try:
            # 使用视频下载URL进行文字提取
            video_url = video['video_download_url'] or video['aweme_url']
            
            if not video_url:
                raise Exception("没有可用的视频URL")
            
            # 提取文字
            extracted_text = await extract_text_from_video(video_url)
            
            result['extracted_text'] = extracted_text
            result['status'] = 'success'
            success_count += 1
            
            log_to_file(f"处理成功: {extracted_text[:100]}...")
            
        except Exception as e:
            error_msg = str(e)
            result['error'] = error_msg
            failed_count += 1
            log_to_file(f"处理失败: {error_msg}")
        
        results.append(result)
        
        # 添加延迟，避免API限制
        if i < len(videos):
            log_to_file("等待3秒...")
            await asyncio.sleep(3)
    
    # 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存JSON结果
    json_filename = f"batch_results_{timestamp}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    log_to_file(f"JSON结果已保存到: {json_filename}")
    
    # 保存文本结果
    txt_filename = f"extracted_texts_{timestamp}.txt"
    with open(txt_filename, 'w', encoding='utf-8') as f:
        f.write(f"抖音视频文字提取结果\n")
        f.write(f"提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"成功提取: {success_count}/{len(videos)} 个视频\n")
        f.write(f"成功率: {success_count/len(videos)*100:.1f}%\n")
        f.write(f"{'='*60}\n\n")
        
        for result in results:
            f.write(f"视频 {result['index']}: {result['title']}\n")
            f.write(f"作者: {result['nickname']}\n")
            f.write(f"链接: {result['aweme_url']}\n")
            f.write(f"点赞: {result['liked_count']} | 评论: {result['comment_count']} | 分享: {result['share_count']}\n")
            f.write(f"状态: {result['status']}\n")
            
            if result['status'] == 'success':
                f.write(f"提取文字:\n{result['extracted_text']}\n")
            else:
                f.write(f"错误信息: {result['error']}\n")
            
            f.write(f"{'-'*40}\n\n")
    
    log_to_file(f"文本结果已保存到: {txt_filename}")
    log_to_file(f"处理完成！成功: {success_count}, 失败: {failed_count}")
    log_to_file(f"成功率: {success_count/len(videos)*100:.1f}%")

if __name__ == "__main__":
    asyncio.run(process_videos_from_json())
