#!/usr/bin/env python3
"""
自动化JSON文件批量视频文字提取工具
直接处理指定的JSON文件，无需用户交互
"""

import os
import re
import json
import time
import asyncio
import requests
from datetime import datetime
from urllib import request
from http import HTTPStatus
import dashscope

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-d4d314c9749c474d95efbb2e85b0769b'
dashscope.api_key = os.getenv('DASHSCOPE_API_KEY')

# 请求头，模拟移动端访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) EdgiOS/121.0.2277.107 Version/17.0 Mobile/15E148 Safari/604.1'
}

class AutoJSONProcessor:
    def __init__(self):
        self.results = []
        self.success_count = 0
        self.failed_count = 0
    
    def load_json_file(self, json_file_path):
        """加载JSON文件"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"📁 成功加载 {json_file_path}，包含 {len(data)} 个视频")
            return data
        except Exception as e:
            print(f"❌ 加载文件出错: {str(e)}")
            return []
    
    def extract_video_info(self, video_data):
        """从JSON数据中提取视频信息"""
        videos = []
        for item in video_data:
            video_info = {
                'aweme_id': item.get('aweme_id', ''),
                'title': item.get('title', '无标题'),
                'desc': item.get('desc', ''),
                'nickname': item.get('nickname', '未知用户'),
                'aweme_url': item.get('aweme_url', ''),
                'video_download_url': item.get('video_download_url', ''),
                'liked_count': item.get('liked_count', '0'),
                'comment_count': item.get('comment_count', '0'),
                'share_count': item.get('share_count', '0')
            }
            if video_info['aweme_url']:
                videos.append(video_info)
        return videos
    
    async def extract_text_from_video(self, video_url, model="paraformer-v2"):
        """从视频URL提取文字"""
        try:
            print(f"🎵 开始语音识别...")
            
            # 使用阿里云百炼API进行语音识别
            recognition = dashscope.audio.asr.Recognition()
            result = recognition.call(
                model=model,
                file_urls=[video_url],
                language_hints=['zh', 'en']
            )
            
            if result.status_code == HTTPStatus.OK:
                if hasattr(result.output, 'results') and result.output.results:
                    # 提取所有文本片段
                    text_segments = []
                    for item in result.output.results:
                        if hasattr(item, 'transcripts') and item.transcripts:
                            for transcript in item.transcripts:
                                if hasattr(transcript, 'text'):
                                    text_segments.append(transcript.text)
                    
                    full_text = ' '.join(text_segments).strip()
                    if full_text:
                        print(f"✅ 文字提取成功，长度: {len(full_text)} 字符")
                        return full_text
                    else:
                        return "未检测到语音内容"
                else:
                    return "语音识别结果为空"
            else:
                error_msg = f"API调用失败: {result.message}"
                print(f"❌ {error_msg}")
                return f"提取失败: {error_msg}"
                
        except Exception as e:
            error_msg = f"文字提取出错: {str(e)}"
            print(f"❌ {error_msg}")
            return error_msg
    
    async def process_single_video(self, video_info, index, total):
        """处理单个视频"""
        print(f"\n{'='*60}")
        print(f"📹 处理视频 {index}/{total}")
        print(f"🏷️  标题: {video_info['title'][:50]}...")
        print(f"👤 作者: {video_info['nickname']}")
        print(f"🔗 链接: {video_info['aweme_url']}")
        
        result = {
            'index': index,
            'aweme_id': video_info['aweme_id'],
            'title': video_info['title'],
            'nickname': video_info['nickname'],
            'aweme_url': video_info['aweme_url'],
            'video_download_url': video_info['video_download_url'],
            'liked_count': video_info['liked_count'],
            'comment_count': video_info['comment_count'],
            'share_count': video_info['share_count'],
            'extracted_text': '',
            'status': 'failed',
            'error': '',
            'process_time': datetime.now().isoformat()
        }
        
        try:
            # 使用视频下载URL进行文字提取
            video_url = video_info['video_download_url'] or video_info['aweme_url']
            
            if not video_url:
                raise Exception("没有可用的视频URL")
            
            # 提取文字
            extracted_text = await self.extract_text_from_video(video_url)
            
            result['extracted_text'] = extracted_text
            result['status'] = 'success'
            self.success_count += 1
            
            print(f"✅ 处理成功")
            print(f"📝 提取文字: {extracted_text[:100]}...")
            
        except Exception as e:
            error_msg = str(e)
            result['error'] = error_msg
            self.failed_count += 1
            print(f"❌ 处理失败: {error_msg}")
        
        self.results.append(result)
        return result
    
    async def batch_process_json(self, json_file_path, delay=3):
        """批量处理JSON文件中的视频"""
        print(f"🚀 开始批量处理JSON文件: {json_file_path}")
        
        # 加载JSON数据
        video_data = self.load_json_file(json_file_path)
        if not video_data:
            return
        
        # 提取视频信息
        videos = self.extract_video_info(video_data)
        if not videos:
            print("❌ 没有找到有效的视频信息")
            return
        
        print(f"📊 找到 {len(videos)} 个有效视频")
        print(f"⏱️  处理间隔: {delay}秒")
        print(f"🕐 预计总时间: {len(videos) * delay / 60:.1f}分钟")
        
        # 显示视频预览
        print(f"\n📋 视频列表预览:")
        for i, video in enumerate(videos[:5], 1):
            print(f"{i}. {video['title'][:50]}... (作者: {video['nickname']})")
        if len(videos) > 5:
            print(f"... 还有 {len(videos) - 5} 个视频")
        
        print(f"\n🚀 开始自动批量提取文字内容...")
        
        # 开始处理
        start_time = time.time()
        
        for i, video in enumerate(videos, 1):
            await self.process_single_video(video, i, len(videos))
            
            # 添加延迟，避免API限制
            if i < len(videos):
                print(f"⏳ 等待 {delay} 秒...")
                await asyncio.sleep(delay)
        
        # 处理完成
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n🎉 批量处理完成！")
        print(f"⏱️  总耗时: {total_time/60:.1f}分钟")
        print(f"✅ 成功: {self.success_count}")
        print(f"❌ 失败: {self.failed_count}")
        print(f"📊 成功率: {self.success_count/(self.success_count+self.failed_count)*100:.1f}%")
        
        # 保存结果
        self.save_results()
        self.save_text_only()
    
    def save_results(self):
        """保存完整结果到JSON文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"auto_batch_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 完整结果已保存到: {filename}")
    
    def save_text_only(self):
        """保存纯文本结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"auto_extracted_texts_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"抖音视频文字提取结果\n")
            f.write(f"提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"成功提取: {self.success_count} 个视频\n")
            f.write(f"{'='*60}\n\n")
            
            for result in self.results:
                if result['status'] == 'success':
                    f.write(f"视频 {result['index']}: {result['title']}\n")
                    f.write(f"作者: {result['nickname']}\n")
                    f.write(f"链接: {result['aweme_url']}\n")
                    f.write(f"点赞: {result['liked_count']} | 评论: {result['comment_count']} | 分享: {result['share_count']}\n")
                    f.write(f"提取文字:\n{result['extracted_text']}\n")
                    f.write(f"{'-'*40}\n\n")
        
        print(f"📝 纯文本结果已保存到: {filename}")

async def main():
    """主函数"""
    print("🎬 自动化JSON批量视频文字提取工具")
    print("="*50)
    
    # 检查API密钥
    if not os.getenv('DASHSCOPE_API_KEY'):
        print("❌ 请设置 DASHSCOPE_API_KEY 环境变量")
        return
    
    processor = AutoJSONProcessor()
    
    # 默认JSON文件路径
    json_file = "json/search_contents_2025-09-11.json"
    
    if not os.path.exists(json_file):
        print(f"❌ 文件不存在: {json_file}")
        return
    
    # 设置处理间隔为3秒
    delay = 3
    
    # 开始批量处理
    await processor.batch_process_json(json_file, delay)

if __name__ == "__main__":
    asyncio.run(main())
