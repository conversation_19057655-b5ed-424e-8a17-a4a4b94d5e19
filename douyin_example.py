#!/usr/bin/env python3
"""
抖音MCP服务器Python模块使用示例
"""

import os
import asyncio
import json
from douyin_mcp_server.server import DouyinProcessor

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-d4d314c9749c474d95efbb2e85b0769b'

async def example_usage():
    """完整使用示例"""
    
    # 示例抖音链接（请替换为真实链接）
    share_link = "https://v.douyin.com/iJsLnno/"  # 请替换为真实链接
    
    print("🎬 抖音MCP服务器使用示例")
    print("=" * 50)
    
    # 初始化处理器
    processor = DouyinProcessor(os.getenv('DASHSCOPE_API_KEY'))
    
    try:
        # 1. 解析视频信息
        print("\n📱 步骤1: 解析视频信息")
        video_info = processor.parse_share_url(share_link)
        print(f"✅ 视频ID: {video_info['video_id']}")
        print(f"✅ 标题: {video_info['title']}")
        print(f"✅ 下载URL: {video_info['url'][:50]}...")
        
        # 2. 获取下载链接（无需API密钥）
        print("\n🔗 步骤2: 获取下载链接")
        download_info = {
            "video_id": video_info["video_id"],
            "title": video_info["title"],
            "download_url": video_info["url"],
            "status": "success"
        }
        print(f"✅ 下载链接已获取")
        
        # 3. 提取文本内容（需要API密钥）
        print("\n📝 步骤3: 提取文本内容")
        try:
            text_content = processor.extract_text_from_video_url(video_info['url'])
            print(f"✅ 文本内容: {text_content}")
        except Exception as e:
            print(f"⚠️ 文本提取失败: {str(e)}")
        
        # 4. 保存结果到文件
        print("\n💾 步骤4: 保存结果")
        result = {
            "video_info": video_info,
            "download_info": download_info,
            "text_content": text_content if 'text_content' in locals() else None
        }
        
        with open('douyin_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print("✅ 结果已保存到 douyin_result.json")
        
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")

def batch_process_links():
    """批量处理多个链接"""
    links = [
        "https://v.douyin.com/link1/",  # 请替换为真实链接
        "https://v.douyin.com/link2/",  # 请替换为真实链接
        # 添加更多链接...
    ]
    
    print("🔄 批量处理模式")
    processor = DouyinProcessor(os.getenv('DASHSCOPE_API_KEY'))
    results = []
    
    for i, link in enumerate(links, 1):
        print(f"\n处理第 {i}/{len(links)} 个链接...")
        try:
            video_info = processor.parse_share_url(link)
            results.append({
                "link": link,
                "video_id": video_info["video_id"],
                "title": video_info["title"],
                "download_url": video_info["url"],
                "status": "success"
            })
            print(f"✅ 成功: {video_info['title']}")
        except Exception as e:
            results.append({
                "link": link,
                "status": "error",
                "error": str(e)
            })
            print(f"❌ 失败: {str(e)}")
    
    # 保存批量结果
    with open('batch_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"\n✅ 批量处理完成，结果保存到 batch_results.json")

class DouyinAPI:
    """封装的API类"""
    
    def __init__(self, api_key=None):
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        self.processor = DouyinProcessor(self.api_key)
    
    def get_video_info(self, share_link):
        """获取视频信息"""
        return self.processor.parse_share_url(share_link)
    
    def get_download_link(self, share_link):
        """获取下载链接"""
        video_info = self.processor.parse_share_url(share_link)
        return video_info['url']
    
    async def extract_text(self, share_link):
        """提取文本"""
        video_info = self.processor.parse_share_url(share_link)
        return self.processor.extract_text_from_video_url(video_info['url'])
    
    def download_video(self, share_link, save_path=None):
        """下载视频到本地"""
        import requests
        
        video_info = self.processor.parse_share_url(share_link)
        download_url = video_info['url']
        
        if not save_path:
            save_path = f"{video_info['video_id']}.mp4"
        
        print(f"📥 正在下载: {video_info['title']}")
        response = requests.get(download_url, stream=True)
        
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        print(f"✅ 下载完成: {save_path}")
        return save_path

if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 完整示例")
    print("2. 批量处理")
    print("3. API类示例")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        asyncio.run(example_usage())
    elif choice == "2":
        batch_process_links()
    elif choice == "3":
        # API类使用示例
        api = DouyinAPI()
        link = input("请输入抖音链接: ").strip()
        if link:
            try:
                info = api.get_video_info(link)
                print(f"视频信息: {info}")
                
                download_url = api.get_download_link(link)
                print(f"下载链接: {download_url}")
            except Exception as e:
                print(f"错误: {e}")
    else:
        print("无效选择")
